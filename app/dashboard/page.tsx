import { getManagerAppraisals, getManagerPerformanceStats } from "@/lib/data/index"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { PerformanceSpiderGraph } from "@/components/performance-spider-graph"
import { ErrorBoundary } from "@/components/error-boundary"
import { AppraisalDashboardWrapper } from "@/components/appraisal-dashboard-wrapper"
import { Users, UserPlus, BarChart3 } from "lucide-react"
import Link from "next/link"
import type { EmployeeAppraisal } from "@/lib/types"

export default async function ManagerDashboardPage() {
  console.log('[DASHBOARD] Loading manager dashboard')

  let appraisals: EmployeeAppraisal[] = []
  let performanceStats
  
  try {
    // Load both appraisals and performance stats in parallel
    const [appraisalsData, statsData] = await Promise.all([
      getManagerAppraisals(),
      getManagerPerformanceStats()
    ])
    
    appraisals = appraisalsData
    performanceStats = statsData
    
    console.log('[DASHBOARD] Loaded', appraisals.length, 'appraisals and performance stats')
  } catch (error) {
    console.log('[DASHBOARD] Error loading data:', error)
    appraisals = []
    performanceStats = {
      total: 0,
      belowExpectations: 0,
      meetsExpectations: 0,
      exceedsExpectations: 0,
      notStarted: 0,
      submittedCount: 0,
      draftCount: 0,
    }
  }

  return (
    <div className="p-4 sm:p-6 space-y-8">
      {/* Page Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">Manager Dashboard</h1>
        <p className="text-muted-foreground">July 2025 appraisal period overview and team management.</p>
      </div>

      {/* Performance Overview - Top Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <BarChart3 className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">Performance Overview</h2>
        </div>
        <ErrorBoundary>
          <PerformanceSpiderGraph 
            stats={performanceStats}
            title="Team Performance Distribution"
            description="Overview of your team's performance ratings and completion status"
          />
        </ErrorBoundary>
      </div>

      {/* Team Section - Bottom Section */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 mb-4">
          <Users className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-semibold">My Team</h2>
        </div>
        
{appraisals.length === 0 ? (
          <Card className="text-center py-12">
            <CardContent>
              <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">No team members assigned</h3>
              <p className="text-muted-foreground mb-6">
                Get started by adding employees to your team to begin the appraisal process.
              </p>
              <Button asChild>
                <Link href="/dashboard/add-people">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add People
                </Link>
              </Button>
            </CardContent>
          </Card>
        ) : (
          <AppraisalDashboardWrapper data={appraisals} />
        )}
      </div>
    </div>
  )
}
