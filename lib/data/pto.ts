import { db } from '../db'
import { getEmployees } from './employees'
import { cache } from '../cache'

export async function getPTOBalance(employeeId: string, year?: number): Promise<any> {
  try {
    const balance = await db.getPTOBalance(employeeId, year)
    return {
      id: balance.id,
      employeeId: balance.employee_id,
      year: balance.year,
      totalDays: balance.total_days,
      usedDays: balance.used_days,
      availableDays: balance.available_days,
      createdAt: balance.created_at,
      updatedAt: balance.updated_at
    }
  } catch (error) {
    console.error('Failed to fetch PTO balance:', error)
    // Return default balance if error occurs
    return {
      id: '',
      employeeId: employeeId,
      year: year || new Date().getFullYear(),
      totalDays: 7,
      usedDays: 0,
      availableDays: 7,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
  }
}

export async function getPTOBalanceForCurrentUser(): Promise<any> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      throw new Error('No authenticated user found')
    }
    
    // Get the employee record for the current user
    const employees = await getEmployees()
    const employee = employees.find(emp => emp.managerId === currentUser.id)
    
    if (!employee) {
      throw new Error('No employee record found for current user')
    }
    
    return await getPTOBalance(employee.id)
  } catch (error) {
    console.error('Failed to fetch PTO balance for current user:', error)
    return null
  }
}

export async function getPTORequestsForManager(managerId?: string, status?: string): Promise<any[]> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    const filterManagerId = managerId || currentUser.id
    const filters: any = { managerId: filterManagerId }
    
    if (status) {
      filters.status = status as 'pending' | 'approved' | 'rejected' | 'cancelled'
    }
    
    const requests = await db.getPTORequests(filters)
    
    return requests.map(request => ({
      id: request.id,
      employeeId: request.employee_id,
      employeeName: request.employee_name,
      managerId: request.manager_id,
      managerName: request.manager_name,
      requestType: request.request_type,
      startDate: request.start_date,
      endDate: request.end_date,
      daysRequested: request.days_requested,
      reason: request.reason,
      status: request.status,
      approvedBy: request.approved_by,
      approvedAt: request.approved_at,
      rejectedReason: request.rejected_reason,
      createdAt: request.created_at,
      updatedAt: request.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch PTO requests for manager:', error)
    return []
  }
}

export async function getPTORequestsForEmployee(employeeId?: string): Promise<any[]> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return []
    }
    
    // If no employeeId provided, get current user's employee record
    let targetEmployeeId = employeeId
    if (!targetEmployeeId) {
      const employees = await getEmployees()
      const employee = employees.find(emp => emp.managerId === currentUser.id)
      if (!employee) {
        return []
      }
      targetEmployeeId = employee.id
    }
    
    const requests = await db.getPTORequests({ employeeId: targetEmployeeId })
    
    return requests.map(request => ({
      id: request.id,
      employeeId: request.employee_id,
      employeeName: request.employee_name,
      managerId: request.manager_id,
      managerName: request.manager_name,
      requestType: request.request_type,
      startDate: request.start_date,
      endDate: request.end_date,
      daysRequested: request.days_requested,
      reason: request.reason,
      status: request.status,
      approvedBy: request.approved_by,
      approvedAt: request.approved_at,
      rejectedReason: request.rejected_reason,
      createdAt: request.created_at,
      updatedAt: request.updated_at
    }))
  } catch (error) {
    console.error('Failed to fetch PTO requests for employee:', error)
    return []
  }
}

export async function getPTODashboardData(): Promise<any> {
  try {
    const { getCurrentUser, hasSuperAdminAccess } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return null
    }
    
    // Super-admins get a comprehensive view of all PTO data
    if (hasSuperAdminAccess(currentUser)) {
      // Super-admin view - show all PTO data across departments
      const allRequests = await db.getPTORequests({}) // Get all requests
      const allStats = await db.getPTOStats() // Get system-wide stats
      
      return {
        type: 'manager', // Use manager view for super-admin
        pendingRequests: allRequests.filter(r => r.status === 'pending'),
        teamRequests: allRequests,
        stats: {
          totalRequests: allStats.totalRequests,
          pendingRequests: allStats.pendingRequests,
          approvedRequests: allStats.approvedRequests,
          rejectedRequests: allStats.rejectedRequests
        }
      }
    }
    
    // Get employees managed by current user (for managers)
    const employees = await getEmployees()
    const managedEmployees = employees.filter(emp => emp.managerId === currentUser.id)

    console.log('🔍 [DEBUG] PTO - Current user ID:', currentUser.id)
    console.log('🔍 [DEBUG] PTO - Managed employees:', managedEmployees.map(emp => ({
      id: emp.id,
      fullName: emp.fullName,
      managerId: emp.managerId
    })))
    
    // Check if user should get manager view (managers with employees, or admin roles)
    const shouldGetManagerView = (currentUser.role === 'manager' && managedEmployees.length > 0) ||
                                currentUser.role === 'hr-admin' ||
                                currentUser.role === 'admin'
    
    if (shouldGetManagerView) {
      // Manager/Admin view - show pending requests from their team or all requests for admins
      const pendingRequests = await getPTORequestsForManager(currentUser.id, 'pending')
      const allRequests = await getPTORequestsForManager(currentUser.id)
      const stats = await db.getPTOStats(currentUser.id)
      
      return {
        type: 'manager',
        pendingRequests,
        teamRequests: allRequests,
        stats: {
          totalRequests: stats.totalRequests,
          pendingRequests: stats.pendingRequests,
          approvedRequests: stats.approvedRequests,
          rejectedRequests: stats.rejectedRequests
        }
      }
    } else {
      // Employee view - show their own balance and requests
      const employee = employees.find(emp => emp.managerId === currentUser.id)
      if (!employee) {
        return null
      }
      
      const balance = await getPTOBalance(employee.id)
      const recentRequests = await getPTORequestsForEmployee(employee.id)
      
      return {
        type: 'employee',
        balance,
        recentRequests,
        stats: {
          totalRequests: recentRequests.length,
          pendingRequests: recentRequests.filter(r => r.status === 'pending').length,
          approvedRequests: recentRequests.filter(r => r.status === 'approved').length,
          rejectedRequests: recentRequests.filter(r => r.status === 'rejected').length
        }
      }
    }
  } catch (error) {
    console.error('Failed to fetch PTO dashboard data:', error)
    return null
  }
}

export async function getPTOStats(managerId?: string): Promise<any> {
  try {
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    
    if (!currentUser) {
      console.error('No authenticated user found')
      return {
        totalRequests: 0,
        pendingRequests: 0,
        approvedRequests: 0,
        rejectedRequests: 0
      }
    }
    
    const filterManagerId = managerId || currentUser.id
    const stats = await db.getPTOStats(filterManagerId)
    
    return {
      totalRequests: stats.totalRequests,
      pendingRequests: stats.pendingRequests,
      approvedRequests: stats.approvedRequests,
      rejectedRequests: stats.rejectedRequests
    }
  } catch (error) {
    console.error('Failed to fetch PTO stats:', error)
    return {
      totalRequests: 0,
      pendingRequests: 0,
      approvedRequests: 0,
      rejectedRequests: 0
    }
  }
}

export async function checkPTOAvailability(employeeId: string, daysRequested: number): Promise<boolean> {
  try {
    return await db.checkPTOAvailability(employeeId, daysRequested)
  } catch (error) {
    console.error('Failed to check PTO availability:', error)
    return false
  }
}

export async function initializeMarketingDepartmentPTO(): Promise<void> {
  try {
    // Get all employees in Marketing department
    const employees = await getEmployees()
    const marketingEmployees = employees.filter(emp => emp.departmentName === 'Marketing')
    const employeeIds = marketingEmployees.map(emp => emp.id)
    
    if (employeeIds.length > 0) {
      await db.initializePTOBalances(employeeIds, new Date().getFullYear(), 7)
      // console.log(`Initialized PTO balances for ${employeeIds.length} Marketing department employees`)
    }
  } catch (error) {
    console.error('Failed to initialize Marketing department PTO:', error)
    throw error
  }
}